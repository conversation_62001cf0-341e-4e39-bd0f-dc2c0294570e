/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.kafka.clients.consumer;

import org.junit.jupiter.api.Test;

import java.time.Duration;
import java.util.Optional;

import static org.apache.kafka.clients.consumer.CloseOptions.GroupMembershipOperation.DEFAULT;
import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

class CloseOptionsTest {

    @Test
    void operationShouldNotBeNull() {
        assertThrows(NullPointerException.class, () -> CloseOptions.groupMembershipOperation(null));
        assertThrows(NullPointerException.class, () -> CloseOptions.timeout(Duration.ZERO).withGroupMembershipOperation(null));
    }

    @Test
    void operationShouldHaveDefaultValue() {
        assertEquals(DEFAULT, CloseOptions.timeout(Duration.ZERO).groupMembershipOperation());
    }

    @Test
    void timeoutCouldBeNull() {
        CloseOptions closeOptions = assertDoesNotThrow(() -> CloseOptions.timeout(null));
        assertEquals(Optional.empty(), closeOptions.timeout());
    }

    @Test
    void timeoutShouldBeDefaultEmpty() {
        assertEquals(Optional.empty(), CloseOptions.groupMembershipOperation(DEFAULT).timeout());
    }
}