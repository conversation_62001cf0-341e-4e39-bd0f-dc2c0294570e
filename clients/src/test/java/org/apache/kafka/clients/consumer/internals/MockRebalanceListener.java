/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.kafka.clients.consumer.internals;

import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.common.TopicPartition;

import java.util.Collection;

public class MockRebalanceListener implements ConsumerRebalanceListener {
    public Collection<TopicPartition> lost;
    public Collection<TopicPartition> revoked;
    public Collection<TopicPartition> assigned;
    public int lostCount = 0;
    public int revokedCount = 0;
    public int assignedCount = 0;

    @Override
    public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
        this.assigned = partitions;
        assignedCount++;
    }

    @Override
    public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
        this.revoked = partitions;
        revokedCount++;
    }

    @Override
    public void onPartitionsLost(Collection<TopicPartition> partitions) {
        this.lost = partitions;
        lostCount++;
    }
}
