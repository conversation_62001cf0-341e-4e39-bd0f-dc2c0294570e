/*
 * Licensed to the Apache Software Foundation (ASF) under one or more
 * contributor license agreements. See the NOTICE file distributed with
 * this work for additional information regarding copyright ownership.
 * The ASF licenses this file to You under the Apache License, Version 2.0
 * (the "License"); you may not use this file except in compliance with
 * the License. You may obtain a copy of the License at
 *
 *    http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package org.apache.kafka.connect.runtime;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

public class SinkConnectorOffsetFlushTest {

    @Test
    public void testOffsetFlushIntervalFallbackToGlobalConfig() {
        Map<String, String> workerProps = new HashMap<>();
        workerProps.put("key.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("value.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("offset.storage.file.filename", "/tmp/connect.offsets");
        workerProps.put("offset.flush.interval.ms", "30000");
        WorkerConfig workerConfig = new StandaloneConfig(workerProps);

        // Should fall back to global config when no connector-specific config is provided
        assertEquals(30000L, workerConfig.offsetCommitInterval("my-connector"));
    }

    @Test
    public void testOffsetFlushIntervalConnectorSpecific() {
        Map<String, String> workerProps = new HashMap<>();
        workerProps.put("key.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("value.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("offset.storage.file.filename", "/tmp/connect.offsets");
        workerProps.put("offset.flush.interval.ms", "30000");
        workerProps.put("connector.offset.flush.interval.ms.my-connector", "10000"); // Connector-specific config
        WorkerConfig workerConfig = new StandaloneConfig(workerProps);

        // Should use connector-specific config
        assertEquals(10000L, workerConfig.offsetCommitInterval("my-connector"));
        // Other connectors should still use global config
        assertEquals(30000L, workerConfig.offsetCommitInterval("other-connector"));
    }

    @Test
    public void testOffsetFlushIntervalZeroValue() {
        Map<String, String> workerProps = new HashMap<>();
        workerProps.put("key.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("value.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("offset.storage.file.filename", "/tmp/connect.offsets");
        workerProps.put("offset.flush.interval.ms", "30000");
        workerProps.put("connector.offset.flush.interval.ms.fast-connector", "0"); // Minimum allowed value
        WorkerConfig workerConfig = new StandaloneConfig(workerProps);

        // Should use connector-specific config even if it's 0
        assertEquals(0L, workerConfig.offsetCommitInterval("fast-connector"));
    }

    @Test
    public void testOffsetFlushIntervalInvalidValue() {
        Map<String, String> workerProps = new HashMap<>();
        workerProps.put("key.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("value.converter", "org.apache.kafka.connect.json.JsonConverter");
        workerProps.put("offset.storage.file.filename", "/tmp/connect.offsets");
        workerProps.put("offset.flush.interval.ms", "30000");
        workerProps.put("connector.offset.flush.interval.ms.bad-connector", "invalid"); // Invalid value
        WorkerConfig workerConfig = new StandaloneConfig(workerProps);

        // Should throw ConfigException for invalid value
        assertThrows(org.apache.kafka.common.config.ConfigException.class,
                () -> workerConfig.offsetCommitInterval("bad-connector"));
    }
}
