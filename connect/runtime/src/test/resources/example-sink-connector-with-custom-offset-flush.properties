# Example worker configuration demonstrating per-connector offset flush intervals
# This configuration would be placed in the worker configuration file (e.g., connect-distributed.properties)

# Global offset flush interval (default for all connectors)
offset.flush.interval.ms=60000

# Per-connector offset flush intervals configured at the broker/worker level
# Format: connector.offset.flush.interval.ms.<connector-name>=<interval-in-ms>
connector.offset.flush.interval.ms.example-sink-connector=10000
connector.offset.flush.interval.ms.high-throughput-connector=5000
connector.offset.flush.interval.ms.low-priority-connector=300000

# Standard worker configurations
key.converter=org.apache.kafka.connect.json.JsonConverter
value.converter=org.apache.kafka.connect.json.JsonConverter
offset.storage.file.filename=/tmp/connect.offsets
